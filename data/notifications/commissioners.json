[{"commissionerId": 32, "notifications": [{"id": "task-2-301", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Hero section design\" is awaiting your review", "timestamp": "2025-07-12T11:51:51.676Z", "isRead": true, "userId": 31, "project": {"id": 301, "title": "Lagos Parks Services website re-design"}, "isFromNetwork": true}, {"id": "task-101-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"User authentication flow design\" is awaiting your review", "timestamp": "2025-07-08T11:39:37.155Z", "isRead": false, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "task-102-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Park booking interface mockups\" is awaiting your review", "timestamp": "2025-07-08T18:12:23.463Z", "isRead": true, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "task-103-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Maintenance reporting feature\" is awaiting your review", "timestamp": "2025-07-10T11:02:22.443Z", "isRead": true, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "task-104-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Navigation and menu structure\" is awaiting your review", "timestamp": "2025-07-12T11:07:10.530Z", "isRead": false, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "task-105-311", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Push notification system design\" is awaiting your review", "timestamp": "2025-07-09T04:24:39.468Z", "isRead": true, "project": {"id": 311, "title": "Lagos Parks Mobile App Development"}, "isFromNetwork": true, "userId": 31}, {"id": "gig-app-network-5", "type": "gig_application", "title": "<PERSON> applied for Interactive Park Map Web App", "message": "1 network application for \"Interactive Park Map Web App\"", "timestamp": "2025-07-13T10:30:00Z", "isRead": true, "gig": {"id": 5, "title": "Interactive Park Map Web App"}, "isFromNetwork": true, "userId": 29}, {"id": "gig-app-public-5", "type": "gig_application", "title": "<PERSON><PERSON> and 1 other applied for Interactive Park Map Web App", "message": "2 applications for \"Interactive Park Map Web App\"", "timestamp": "2025-07-12T14:15:00Z", "isRead": false, "gig": {"id": 5, "title": "Interactive Park Map Web App"}, "isFromNetwork": false, "userId": 1}, {"id": "project-pause-1", "type": "project_pause", "title": "<PERSON> requested a pause for your Brand Animation Package project", "message": "Motion graphics project needs temporary pause", "timestamp": "2025-07-12T21:06:35.376Z", "isRead": false, "project": {"id": 301, "title": "Brand Animation Package"}, "isFromNetwork": true, "userId": 28}, {"id": "project-accepted-1", "type": "project_accepted", "title": "<PERSON> accepted your project", "message": "UI component library project has been accepted and will start soon", "timestamp": "2025-07-13T21:06:35.376Z", "isRead": true, "project": {"id": 302, "title": "UI Component Library"}, "isFromNetwork": true, "userId": 26}, {"id": "proposal-1", "type": "proposal_sent", "title": "<PERSON> sent you a proposal", "message": "New project proposal for video production services", "timestamp": "2025-07-13T21:06:35.376Z", "isRead": false, "isFromNetwork": true, "userId": 25}, {"id": "invoice-1", "type": "invoice_sent", "title": "<PERSON><PERSON><PERSON> Flether sent you a new invoice", "message": "Invoice MF-24BGJ for Lagos Parks Services website re-design", "timestamp": "2025-07-14T18:06:35.376Z", "isRead": true, "invoice": {"number": "MF-24BGJ", "amount": 5244, "projectTitle": "Lagos Parks Services website re-design"}, "isFromNetwork": true, "userId": 31}, {"id": "gig-app-4", "type": "gig_application", "title": "<PERSON><PERSON> applied for Park Visitor Mobile App", "message": "New application for \"Park Visitor Mobile App\"", "timestamp": "2025-07-12T09:15:00Z", "isRead": true, "gig": {"id": 6, "title": "Park Visitor Mobile App"}, "isFromNetwork": false, "userId": 11}, {"id": "gig-app-5", "type": "gig_application", "title": "<PERSON> applied for Digital Signage Content Management", "message": "New application for \"Digital Signage Content Management\"", "timestamp": "2025-07-13T14:30:00Z", "isRead": true, "gig": {"id": 7, "title": "Digital Signage Content Management"}, "isFromNetwork": false, "userId": 2}, {"id": "invoice-paid-1", "type": "invoice_paid", "title": "Invoice payment processed", "message": "Payment for milestone 2 has been processed successfully", "timestamp": "2025-07-18T14:12:50.466Z", "isRead": true, "project": {"id": 1, "title": "Interactive Park Map Web App"}, "isFromNetwork": false}, {"id": "project-pause-accepted-1", "type": "project_pause_accepted", "title": "Project pause request approved", "message": "Your request to pause the Interactive Park Map project has been approved", "timestamp": "2025-07-18T12:12:50.466Z", "isRead": true, "project": {"id": 1, "title": "Interactive Park Map Web App"}, "isFromNetwork": false}, {"id": "storefront-purchase-1", "type": "storefront_purchase", "title": "Mars<PERSON> Fletcher just purchased Poetry Slam Live tickets", "message": "New sale on your storefront", "timestamp": "2025-07-18T15:12:50.466Z", "isRead": true, "isFromNetwork": false}, {"id": "task-202-314", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"High-fidelity UI design and prototyping\" is awaiting your review", "timestamp": "2025-07-16T19:34:28.815Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 314, "title": "E-commerce Platform UI Redesign"}, "isFromNetwork": true}, {"id": "gig-app-6", "type": "gig_application", "title": "<PERSON><PERSON><PERSON> applied for Park Analytics Dashboard", "message": "New application for \"Park Analytics Dashboard\"", "timestamp": "2025-08-03T14:04:43.567Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "gig": {"id": 8, "title": "Park Analytics Dashboard"}, "isFromNetwork": true}, {"id": "gig-app-11", "type": "gig_application", "title": "<PERSON><PERSON><PERSON> applied for UI/UX Design - Lagos State Parks Services", "message": "New application for \"UI/UX Design - Lagos State Parks Services\"", "timestamp": "2025-08-03T22:19:53.645Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "gig": {"id": 9, "title": "UI/UX Design - Lagos State Parks Services"}, "isFromNetwork": true}, {"id": "task-203-315", "type": "task_submission", "title": "<PERSON><PERSON> submitted a task", "message": "\"Initial setup for Interactive Park Map Web App\" is awaiting your review", "timestamp": "2025-08-03T14:00:50.108Z", "isRead": true, "user": {"id": 3, "name": "<PERSON><PERSON>", "avatar": "/avatars/geeko.png"}, "project": {"id": 315, "title": "Interactive Park Map Web App"}, "isFromNetwork": true}, {"id": "gig-app-network-10", "type": "gig_application", "title": "<PERSON><PERSON><PERSON> applied for Advertising Design - Lagos State Parks Services", "message": "1 network application for \"Advertising Design - Lagos State Parks Services\"", "timestamp": "2025-08-04T20:35:35.161Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "gig": {"id": 10, "title": "Advertising Design - Lagos State Parks Services"}, "isFromNetwork": true}, {"id": "gig-app-public-10", "type": "gig_application", "title": "<PERSON><PERSON> applied for Advertising Design - Lagos State Parks Services", "message": "1 application for \"Advertising Design - Lagos State Parks Services\"", "timestamp": "2025-08-03T16:00:00Z", "isRead": false, "user": {"id": 11, "name": "<PERSON><PERSON>", "avatar": "/avatars/riya.png"}, "gig": {"id": 10, "title": "Advertising Design - Lagos State Parks Services"}, "isFromNetwork": false}]}, {"commissionerId": 33, "notifications": []}, {"commissionerId": 34, "notifications": [{"id": "invoice-mgl000306-m1", "type": "invoice_sent", "title": "<PERSON><PERSON><PERSON> Flether sent you a new invoice", "message": "Invoice MGL000306-M1 for Nebula CMS landing redesign", "timestamp": "2025-07-30T10:30:00.000Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "invoice": {"number": "MGL000306-M1", "amount": 2500, "projectTitle": "Nebula CMS landing redesign"}, "context": {"projectId": 306, "invoiceNumber": "MGL000306-M1"}, "metadata": {"invoiceNumber": "MGL000306-M1", "projectTitle": "Nebula CMS landing redesign", "amount": 2500, "freelancerName": "<PERSON><PERSON><PERSON>"}, "isFromNetwork": true}, {"id": "invoice-mgl000303-m1", "type": "invoice_sent", "title": "<PERSON><PERSON><PERSON> Flether sent you a new invoice", "message": "Invoice MGL000303-M1 for Corlax iOS app UX", "timestamp": "2025-07-21T10:30:00.000Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "invoice": {"number": "MGL000303-M1", "amount": 2333.33, "projectTitle": "Corlax iOS app UX"}, "context": {"projectId": 303, "invoiceNumber": "MGL000303-M1"}, "metadata": {"invoiceNumber": "MGL000303-M1", "projectTitle": "Corlax iOS app UX", "amount": 2333.33, "freelancerName": "<PERSON><PERSON><PERSON>"}, "isFromNetwork": true}, {"id": "task-21-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"CTA Button States\" is awaiting your review", "timestamp": "2025-07-30T02:50:37.167Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-22-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Analytics tag setup\" is awaiting your review", "timestamp": "2025-07-31T18:01:16.248Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-23-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Mobile nav menu prototype\" is awaiting your review", "timestamp": "2025-07-31T10:42:52.714Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-24-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Carousel animation mock\" is awaiting your review", "timestamp": "2025-07-30T22:29:24.786Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-25-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"404 error page concept\" is awaiting your review", "timestamp": "2025-08-01T21:11:26.860Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-26-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Global search UI wireframe\" is awaiting your review", "timestamp": "2025-07-31T08:00:34.104Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-27-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Accessibility audit pass\" is awaiting your review", "timestamp": "2025-08-02T06:55:16.672Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-29-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"URGENT: Footer contact form\" is awaiting your review", "timestamp": "2025-08-04T09:30:56.487Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-30-306", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Blog layout template\" is awaiting your review", "timestamp": "2025-08-04T20:27:36.686Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 306, "title": "Nebula CMS landing redesign"}, "isFromNetwork": true}, {"id": "task-6-303", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"Login & onboarding flow\" is awaiting your review", "timestamp": "2025-08-02T19:21:15.585Z", "isRead": false, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 303, "title": "Corlax iOS app UX"}, "isFromNetwork": true}, {"id": "task-7-303", "type": "task_submission", "title": "<PERSON><PERSON><PERSON> submitted a task", "message": "\"iOS testing & deployment\" is awaiting your review", "timestamp": "2025-07-30T06:23:56.429Z", "isRead": true, "user": {"id": 31, "name": "<PERSON><PERSON><PERSON>", "avatar": "/avatars/margsate-flether.png"}, "project": {"id": 303, "title": "Corlax iOS app UX"}, "isFromNetwork": true}, {"id": "invoice-paid-1", "type": "invoice_paid", "title": "Invoice payment processed", "message": "Payment for milestone 2 has been processed successfully", "timestamp": "2025-08-05T18:32:21.223Z", "isRead": false, "project": {"id": 1, "title": "Interactive Park Map Web App"}, "isFromNetwork": false}, {"id": "project-pause-accepted-1", "type": "project_pause_accepted", "title": "Project pause request approved", "message": "Your request to pause the Interactive Park Map project has been approved", "timestamp": "2025-08-05T16:32:21.223Z", "isRead": false, "project": {"id": 1, "title": "Interactive Park Map Web App"}, "isFromNetwork": false}, {"id": "storefront-purchase-1", "type": "storefront_purchase", "title": "Mars<PERSON> Fletcher just purchased Poetry Slam Live tickets", "message": "New sale on your storefront", "timestamp": "2025-08-05T19:32:21.223Z", "isRead": false, "isFromNetwork": false}, {"id": "gig-app-14", "type": "gig_application", "title": "<PERSON><PERSON> applied for Motion Graphics Video for Healthcare App", "message": "New application for \"Motion Graphics Video for Healthcare App\"", "timestamp": "2025-08-06T16:58:01.797Z", "isRead": true, "user": {"id": 1, "name": "<PERSON><PERSON>", "avatar": "/avatars/tobi.png"}, "gig": {"id": 2, "title": "Motion Graphics Video for Healthcare App"}, "isFromNetwork": true}]}]