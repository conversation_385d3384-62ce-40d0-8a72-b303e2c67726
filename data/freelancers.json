[{"id": 1, "userId": 1, "category": "Software Development", "skillCategories": ["Web Development", "Frontend", "Backend"], "tools": ["JavaScript", "TypeScript", "<PERSON>", "VS Code", "GitHub"], "rate": "80-200/hr", "minRate": 80, "maxRate": 200, "location": "Lagos, Nigeria", "rating": 5, "availability": "Away", "completedProjects": 18, "withdrawalMethod": "bank_transfer", "about": "I am a user interface designer with 5+ years of experience designing complex digital experiences with human-centered design for startups. I've worked inside design and programming teams which gives me a broader vision in a graphic and technological context. I am known for always creating products that customers love with excellent interface, interactions (Interaction Design), micro-interactions, sketching, prototyping (Figma) and construction of design systems.", "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/tobi-philly"}, {"platform": "behance", "url": "https://behance.net/tobi-philly"}, {"platform": "website", "url": "https://tobi-philly.dev"}], "responsibilities": ["RFP", "Talent Sourcing", "Adobe XD", "Iconography", "Procurement", "Design Systems", "Graphic Design", "Creative Strategy", "Information Architecture"]}, {"id": 2, "userId": 2, "category": "Design", "skills": ["Adobe Photoshop", "Adobe Illustrator", "<PERSON><PERSON>"], "rate": "50-70/hr", "minRate": 50, "maxRate": 70, "location": "US", "rating": 4, "availability": "Available", "withdrawalMethod": "paypal", "specializations": ["Brand Design"]}, {"id": 3, "userId": 3, "category": "Design", "skills": ["Figma", "Adobe Photoshop", "Adobe Illustrator"], "rate": "80-100/hr", "minRate": 80, "maxRate": 100, "location": "CA", "rating": 4, "availability": "Available", "withdrawalMethod": "bank_transfer", "specializations": ["Interface Design"]}, {"id": 4, "userId": 4, "category": "Marketing", "skills": ["Product Marketing", "User Research", "Go-To-Market Strategy"], "rate": "55/hr", "minRate": 55, "maxRate": 55, "location": "NG", "rating": 5, "availability": "Available", "completedProjects": 13, "withdrawalMethod": "paypal", "specializations": ["Consumer Insights"]}, {"id": 5, "userId": 5, "category": "Creative Writing", "skills": ["Songwriting", "Poetry", "Fiction"], "rate": "35/hr", "minRate": 35, "maxRate": 35, "location": "GH", "rating": 3, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 6, "userId": 6, "category": "Video Production", "skills": ["YouTube", "Shorts", "TikTok"], "rate": "40/hr", "minRate": 40, "maxRate": 40, "location": "PH", "rating": 2, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 7, "userId": 7, "category": "Film & Events", "skills": ["DOP", "Lighting", "Camera"], "rate": "65/hr", "minRate": 65, "maxRate": 65, "location": "UK", "rating": 4, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 8, "userId": 8, "category": "Fashion", "skills": ["Sketching", "Tech Packs", "Trends"], "rate": "45/hr", "minRate": 45, "maxRate": 45, "location": "GH", "rating": 4, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 9, "userId": 9, "category": "Event Production", "skills": ["Stage Design", "Security", "Catering"], "rate": "60/hr", "minRate": 60, "maxRate": 60, "location": "NG", "rating": 5, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 10, "userId": 10, "category": "Printing", "skills": ["Books", "Banners", "Magazines", "Pamphlets"], "rate": "25/hr", "minRate": 25, "maxRate": 25, "location": "ZA", "rating": 2, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 11, "userId": 11, "category": "Software Development", "skills": ["JavaScript", "React", "App Development", "Tailwind"], "rate": "30-50/hr", "minRate": 30, "maxRate": 50, "location": "IN", "rating": 4, "availability": "Available", "withdrawalMethod": "bank_transfer", "specializations": ["Responsive Web"]}, {"id": 12, "userId": 12, "category": "Marketing", "skills": ["UX Marketing", "Campaign Strategy", "Copywriting"], "rate": "70/hr", "minRate": 70, "maxRate": 70, "location": "FR", "rating": 5, "availability": "Available", "withdrawalMethod": "paypal", "specializations": ["Brand Voice"]}, {"id": 13, "userId": 13, "category": "Design", "skills": ["After Effects", "Cinema 4D", "Premiere Pro"], "rate": "80/hr", "minRate": 80, "maxRate": 80, "location": "DE", "rating": 4, "availability": "Available", "withdrawalMethod": "bank_transfer", "specializations": ["Visual Effects"]}, {"id": 14, "userId": 14, "category": "Software Development", "skills": ["Python", "<PERSON>er", "Kubernetes", "CI/CD"], "rate": "90-120/hr", "minRate": 90, "maxRate": 120, "location": "TH", "rating": 5, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 15, "userId": 15, "category": "Marketing", "skills": ["Surveys", "Data Visualization", "Qualitative Research"], "rate": "40/hr", "minRate": 40, "maxRate": 40, "location": "RW", "rating": 3, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 16, "userId": 16, "category": "Software Development", "skills": ["Node.js", "MongoDB", "GraphQL", "Web Development"], "rate": "60/hr", "minRate": 60, "maxRate": 60, "location": "KE", "rating": 5, "availability": "Available", "withdrawalMethod": "paypal", "specializations": ["API Design"]}, {"id": 17, "userId": 17, "category": "Marketing", "skills": ["Calendar Management", "Email <PERSON>ling", "Admin Support"], "rate": "15/hr", "minRate": 15, "maxRate": 15, "location": "ID", "rating": 3, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 18, "userId": 18, "category": "Creative Writing", "skills": ["SEO Writing", "Blogging", "Fiction"], "rate": "45/hr", "minRate": 45, "maxRate": 45, "location": "FR", "rating": 4, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 19, "userId": 19, "category": "Software Development", "skills": ["AWS", "Hardware", "Terraform"], "rate": "100-150/hr", "minRate": 100, "maxRate": 150, "location": "DE", "rating": 5, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 20, "userId": 20, "category": "Marketing", "skills": ["CRM", "Retention Strategy", "Client Onboarding"], "rate": "50/hr", "minRate": 50, "maxRate": 50, "location": "IN", "rating": 4, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 21, "userId": 21, "category": "Software Development", "skills": ["React Native", "<PERSON><PERSON><PERSON>"], "rate": "65-100/hr", "minRate": 65, "maxRate": 100, "location": "SG", "rating": 5, "availability": "Available", "withdrawalMethod": "bank_transfer", "specializations": ["Cross-Platform Apps"]}, {"id": 22, "userId": 22, "category": "Software Development", "skills": ["App Development", "Flutter", "Dart"], "rate": "75-120/hr", "minRate": 75, "maxRate": 120, "location": "MX", "rating": 4, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 23, "userId": 23, "category": "Software Development", "skills": ["Python", "<PERSON><PERSON>", "Django"], "rate": "60-110/hr", "minRate": 60, "maxRate": 110, "location": "NG", "rating": 4, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 24, "userId": 24, "category": "Video Production", "skills": ["Shorts", "TikTok", "YouTube"], "rate": "25-60/hr", "minRate": 25, "maxRate": 60, "location": "KR", "rating": 3, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 25, "userId": 25, "category": "Software Development", "skills": ["Hardware", "Embedded Systems", "IoT"], "rate": "80-130/hr", "minRate": 80, "maxRate": 130, "location": "DE", "rating": 5, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 26, "userId": 26, "category": "Printing", "skills": ["Books", "Banners", "Pamphlets"], "rate": "35/hr", "minRate": 35, "maxRate": 35, "location": "RU", "rating": 3, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 27, "userId": 27, "category": "Event Production", "skills": ["Stage Design", "Security"], "rate": "70-95/hr", "minRate": 70, "maxRate": 95, "location": "NG", "rating": 4, "availability": "Available", "withdrawalMethod": "bank_transfer"}, {"id": 28, "userId": 28, "category": "Creative Writing", "skills": ["Poetry", "Fiction", "Songwriting"], "rate": "28-40/hr", "minRate": 28, "maxRate": 40, "location": "IT", "rating": 3, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 29, "userId": 29, "category": "Software Development", "skills": ["Web Development", "React", "Node.js"], "rate": "95-160/hr", "minRate": 95, "maxRate": 160, "location": "US", "rating": 5, "availability": "Available", "withdrawalMethod": "bank_transfer", "specializations": ["End-to-End Systems"]}, {"id": 30, "userId": 30, "category": "Marketing", "skills": ["Admin Support", "Calendar Management", "Client Onboarding"], "rate": "20-30/hr", "minRate": 20, "maxRate": 30, "location": "UK", "rating": 2, "availability": "Available", "withdrawalMethod": "paypal"}, {"id": 31, "userId": 31, "category": "Design", "skillCategories": ["UI/UX Design", "Graphic Design", "Brand Design"], "tools": ["Figma", "Adobe Illustrator", "Adobe Photoshop", "Sketch"], "rate": "$20 - $50/hr", "minRate": 20, "maxRate": 50, "location": "Lagos, Nigeria", "rating": 4, "availability": "Available", "withdrawalMethod": "bank_transfer", "about": "I am a user interface designer with 5+ years of experience designing complex digital experiences with human-centered design for startups. I've worked inside design and programming teams which gives me a broader vision in a graphic and technological context. I am known for always creating products that customers love with excellent interface, interactions (Interaction Design), micro-interactions, sketching, prototyping (Figma) and construction of design systems.", "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/margsate-flether"}, {"platform": "behance", "url": "https://behance.net/margsate-flether"}, {"platform": "dribbble", "url": "https://dribbble.com/margsate-flether"}, {"platform": "website", "url": "https://margsate.com"}], "responsibilities": ["Wireframes", "Figma", "Adobe XD", "Iconography", "Prototyping", "Design Systems", "Graphic Design", "Effective communication", "Information Architecture"]}]